<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从属连词入门</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white p-6">
    
    <!-- 从属连词概述 -->
    <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-gray-800">从属连词（Subordinating Conjunctions）</h2>
        <p class="text-gray-700 mb-4">从属连词用于连接主句和从句，创建复合句。从句依赖于主句才能表达完整的意思。常见的从属连词包括because、when、if、although等。</p>
        <p class="text-gray-700 mb-4">从属连词引导的从句可以放在句首或句末。当从句在句首时，通常用逗号分隔；当从句在句末时，一般不需要逗号。</p>
    </section>

    <!-- Because - 因为 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Because - 表示原因</h3>
        <p class="text-gray-700 mb-4">Because用于表示原因或理由，回答"为什么"的问题。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">原因从句</div>
                <div class="keyword text-lg mb-1">I stayed home because it was raining.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ steɪd hoʊm bɪˈkɔz ɪt wʌz ˈreɪnɪŋ/</div>
                <div class="text-gray-700">我待在家里，因为下雨了。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">从句在句首</div>
                <div class="keyword text-lg mb-1">Because she studied hard, she passed the exam.</div>
                <div class="text-sm text-gray-600 mb-1">/bɪˈkɔz ʃi ˈstʌdid hɑrd, ʃi pæst ði ɪɡˈzæm/</div>
                <div class="text-gray-700">因为她努力学习，所以通过了考试。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">日常对话</div>
                <div class="keyword text-lg mb-1">He's tired because he worked late.</div>
                <div class="text-sm text-gray-600 mb-1">/hiz ˈtaɪrd bɪˈkɔz hi wɜrkt leɪt/</div>
                <div class="text-gray-700">他很累，因为他工作到很晚。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">情感原因</div>
                <div class="keyword text-lg mb-1">Because I love you, I will help you.</div>
                <div class="text-sm text-gray-600 mb-1">/bɪˈkɔz aɪ lʌv ju, aɪ wɪl hɛlp ju/</div>
                <div class="text-gray-700">因为我爱你，所以我会帮助你。</div>
            </div>
        </div>
    </section>

    <!-- When - 当...时候 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">When - 表示时间</h3>
        <p class="text-gray-700 mb-4">When用于表示时间关系，指明某个动作或事件发生的时间。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">时间从句</div>
                <div class="keyword text-lg mb-1">When I arrived, the party had started.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛn aɪ əˈraɪvd, ðə ˈpɑrti hæd ˈstɑrtəd/</div>
                <div class="text-gray-700">当我到达时，聚会已经开始了。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">习惯动作</div>
                <div class="keyword text-lg mb-1">I feel happy when I listen to music.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ fil ˈhæpi wɛn aɪ ˈlɪsən tu ˈmjuzɪk/</div>
                <div class="text-gray-700">当我听音乐时，我感到快乐。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">过去时间</div>
                <div class="keyword text-lg mb-1">When she was young, she lived in Paris.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛn ʃi wʌz jʌŋ, ʃi lɪvd ɪn ˈpɛrɪs/</div>
                <div class="text-gray-700">当她年轻时，她住在巴黎。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">将来时间</div>
                <div class="keyword text-lg mb-1">When you finish your work, call me.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛn ju ˈfɪnɪʃ jʊr wɜrk, kɔl mi/</div>
                <div class="text-gray-700">当你完成工作时，给我打电话。</div>
            </div>
        </div>
    </section>

    <!-- If - 如果 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">If - 表示条件</h3>
        <p class="text-gray-700 mb-4">If用于表示条件，引导条件从句。条件句分为真实条件句和虚拟条件句。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">真实条件</div>
                <div class="keyword text-lg mb-1">If it rains tomorrow, we'll stay inside.</div>
                <div class="text-sm text-gray-600 mb-1">/ɪf ɪt reɪnz təˈmɔroʊ, wil steɪ ɪnˈsaɪd/</div>
                <div class="text-gray-700">如果明天下雨，我们就待在室内。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">建议条件</div>
                <div class="keyword text-lg mb-1">If you study hard, you will succeed.</div>
                <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑrd, ju wɪl səkˈsid/</div>
                <div class="text-gray-700">如果你努力学习，你就会成功。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">现在条件</div>
                <div class="keyword text-lg mb-1">If you need help, just ask me.</div>
                <div class="text-sm text-gray-600 mb-1">/ɪf ju nid hɛlp, ʤʌst æsk mi/</div>
                <div class="text-gray-700">如果你需要帮助，尽管问我。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">虚拟条件</div>
                <div class="keyword text-lg mb-1">If I were rich, I would travel the world.</div>
                <div class="text-sm text-gray-600 mb-1">/ɪf aɪ wər rɪʧ, aɪ wʊd ˈtrævəl ðə wɜrld/</div>
                <div class="text-gray-700">如果我很富有，我就会环游世界。</div>
            </div>
        </div>
    </section>

    <!-- Although - 尽管 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Although - 表示让步</h3>
        <p class="text-gray-700 mb-4">Although用于表示让步关系，相当于"尽管、虽然"的意思，引导让步状语从句。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">让步对比</div>
                <div class="keyword text-lg mb-1">Although it was cold, we went swimming.</div>
                <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ɪt wʌz koʊld, wi wɛnt ˈswɪmɪŋ/</div>
                <div class="text-gray-700">尽管天气很冷，我们还是去游泳了。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">意外结果</div>
                <div class="keyword text-lg mb-1">Although he studied little, he passed the test.</div>
                <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ hi ˈstʌdid ˈlɪtəl, hi pæst ðə tɛst/</div>
                <div class="text-gray-700">尽管他学习很少，但还是通过了考试。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">个人感受</div>
                <div class="keyword text-lg mb-1">Although I'm tired, I feel happy.</div>
                <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ aɪm ˈtaɪrd, aɪ fil ˈhæpi/</div>
                <div class="text-gray-700">尽管我很累，但我感到快乐。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">事实对比</div>
                <div class="keyword text-lg mb-1">Although she's young, she's very wise.</div>
                <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ʃiz jʌŋ, ʃiz ˈvɛri waɪz/</div>
                <div class="text-gray-700">尽管她很年轻，但她很聪明。</div>
            </div>
        </div>
    </section>

    <!-- While - 当...时候/然而 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">While - 表示时间或对比</h3>
        <p class="text-gray-700 mb-4">While既可以表示时间关系（当...时候），也可以表示对比关系（然而、而）。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">同时进行</div>
                <div class="keyword text-lg mb-1">While I was cooking, she was reading.</div>
                <div class="text-sm text-gray-600 mb-1">/waɪl aɪ wʌz ˈkʊkɪŋ, ʃi wʌz ˈridɪŋ/</div>
                <div class="text-gray-700">我在做饭的时候，她在看书。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">对比关系</div>
                <div class="keyword text-lg mb-1">While John likes coffee, Mary prefers tea.</div>
                <div class="text-sm text-gray-600 mb-1">/waɪl ʤɑn laɪks ˈkɔfi, ˈmɛri prəˈfɜrz ti/</div>
                <div class="text-gray-700">约翰喜欢咖啡，而玛丽更喜欢茶。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">持续动作</div>
                <div class="keyword text-lg mb-1">While you're here, please help me.</div>
                <div class="text-sm text-gray-600 mb-1">/waɪl jʊr hir, pliz hɛlp mi/</div>
                <div class="text-gray-700">趁你在这里，请帮助我。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">让步对比</div>
                <div class="keyword text-lg mb-1">While the job is difficult, it's rewarding.</div>
                <div class="text-sm text-gray-600 mb-1">/waɪl ðə ʤɑb ɪz ˈdɪfəkəlt, ɪts rɪˈwɔrdɪŋ/</div>
                <div class="text-gray-700">虽然这份工作很困难，但很有回报。</div>
            </div>
        </div>
    </section>

    <!-- Since - 自从/因为 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Since - 表示时间或原因</h3>
        <p class="text-gray-700 mb-4">Since可以表示时间关系（自从...以来）或原因关系（因为、既然）。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">时间起点</div>
                <div class="keyword text-lg mb-1">I have lived here since 2020.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ hæv lɪvd hir sɪns ˈtwɛnti ˈtwɛnti/</div>
                <div class="text-gray-700">我从2020年起就住在这里。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">原因解释</div>
                <div class="keyword text-lg mb-1">Since you're tired, you should rest.</div>
                <div class="text-sm text-gray-600 mb-1">/sɪns jʊr ˈtaɪrd, ju ʃʊd rɛst/</div>
                <div class="text-gray-700">既然你累了，你应该休息。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">时间延续</div>
                <div class="keyword text-lg mb-1">Since I started working, I've been busy.</div>
                <div class="text-sm text-gray-600 mb-1">/sɪns aɪ ˈstɑrtəd ˈwɜrkɪŋ, aɪv bɪn ˈbɪzi/</div>
                <div class="text-gray-700">自从我开始工作以来，我一直很忙。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">显然原因</div>
                <div class="keyword text-lg mb-1">Since it's raining, take an umbrella.</div>
                <div class="text-sm text-gray-600 mb-1">/sɪns ɪts ˈreɪnɪŋ, teɪk ən ʌmˈbrɛlə/</div>
                <div class="text-gray-700">既然在下雨，带把伞吧。</div>
            </div>
        </div>
    </section>

    <!-- Before 和 After -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Before 和 After - 表示时间顺序</h3>
        <p class="text-gray-700 mb-4">Before表示"在...之前"，After表示"在...之后"，都用来表示时间的先后顺序。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">之前时间</div>
                <div class="keyword text-lg mb-1">Before you leave, lock the door.</div>
                <div class="text-sm text-gray-600 mb-1">/bɪˈfɔr ju liv, lɑk ðə dɔr/</div>
                <div class="text-gray-700">在你离开之前，锁上门。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">之后时间</div>
                <div class="keyword text-lg mb-1">After I finish work, I'll call you.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈæftər aɪ ˈfɪnɪʃ wɜrk, aɪl kɔl ju/</div>
                <div class="text-gray-700">我工作完成后，会给你打电话。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">过去顺序</div>
                <div class="keyword text-lg mb-1">Before he arrived, we had started dinner.</div>
                <div class="text-sm text-gray-600 mb-1">/bɪˈfɔr hi əˈraɪvd, wi hæd ˈstɑrtəd ˈdɪnər/</div>
                <div class="text-gray-700">在他到达之前，我们已经开始吃晚饭了。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">事件序列</div>
                <div class="keyword text-lg mb-1">After the movie ended, we went home.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈæftər ðə ˈmuvi ˈɛndəd, wi wɛnt hoʊm/</div>
                <div class="text-gray-700">电影结束后，我们回家了。</div>
            </div>
        </div>
    </section>

    <!-- Unless - 除非 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Unless - 表示否定条件</h3>
        <p class="text-gray-700 mb-4">Unless表示"除非"，相当于"if not"，用来表达否定的条件。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">否定条件</div>
                <div class="keyword text-lg mb-1">Unless you study, you won't pass.</div>
                <div class="text-sm text-gray-600 mb-1">/ənˈlɛs ju ˈstʌdi, ju woʊnt pæs/</div>
                <div class="text-gray-700">除非你学习，否则你不会通过。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">必要条件</div>
                <div class="keyword text-lg mb-1">Unless it rains, we'll have a picnic.</div>
                <div class="text-sm text-gray-600 mb-1">/ənˈlɛs ɪt reɪnz, wil hæv ə ˈpɪknɪk/</div>
                <div class="text-gray-700">除非下雨，否则我们会去野餐。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">警告条件</div>
                <div class="keyword text-lg mb-1">Unless you hurry, you'll be late.</div>
                <div class="text-sm text-gray-600 mb-1">/ənˈlɛs ju ˈhɜri, jul bi leɪt/</div>
                <div class="text-gray-700">除非你快点，否则你会迟到。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">唯一例外</div>
                <div class="keyword text-lg mb-1">Unless you help me, I can't finish this.</div>
                <div class="text-sm text-gray-600 mb-1">/ənˈlɛs ju hɛlp mi, aɪ kænt ˈfɪnɪʃ ðɪs/</div>
                <div class="text-gray-700">除非你帮我，否则我无法完成这个。</div>
            </div>
        </div>
    </section>

    <!-- Until - 直到 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Until - 表示时间终点</h3>
        <p class="text-gray-700 mb-4">Until表示"直到...为止"，用来表示动作或状态持续到某个时间点。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">时间终点</div>
                <div class="keyword text-lg mb-1">Wait here until I come back.</div>
                <div class="text-sm text-gray-600 mb-1">/weɪt hir ənˈtɪl aɪ kʌm bæk/</div>
                <div class="text-gray-700">在这里等我，直到我回来。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">持续状态</div>
                <div class="keyword text-lg mb-1">I worked until midnight.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ wɜrkt ənˈtɪl ˈmɪdnaɪt/</div>
                <div class="text-gray-700">我工作到午夜。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">条件时间</div>
                <div class="keyword text-lg mb-1">Until you apologize, I won't talk to you.</div>
                <div class="text-sm text-gray-600 mb-1">/ənˈtɪl ju əˈpɑləˌʤaɪz, aɪ woʊnt tɔk tu ju/</div>
                <div class="text-gray-700">直到你道歉，我才会和你说话。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">等待结果</div>
                <div class="keyword text-lg mb-1">Keep trying until you succeed.</div>
                <div class="text-sm text-gray-600 mb-1">/kip ˈtraɪɪŋ ənˈtɪl ju səkˈsid/</div>
                <div class="text-gray-700">继续努力，直到你成功。</div>
            </div>
        </div>
    </section>

    <!-- As - 当...时候/因为/如同 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">As - 多重含义连词</h3>
        <p class="text-gray-700 mb-4">As是一个多功能连词，可以表示时间（当...时候）、原因（因为）、方式（如同、按照）等多种关系。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">同时进行</div>
                <div class="keyword text-lg mb-1">As I was walking, I saw a bird.</div>
                <div class="text-sm text-gray-600 mb-1">/æz aɪ wʌz ˈwɔkɪŋ, aɪ sɔ ə bɜrd/</div>
                <div class="text-gray-700">当我走路时，我看到了一只鸟。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">原因解释</div>
                <div class="keyword text-lg mb-1">As it was late, we decided to go home.</div>
                <div class="text-sm text-gray-600 mb-1">/æz ɪt wʌz leɪt, wi dɪˈsaɪdəd tu goʊ hoʊm/</div>
                <div class="text-gray-700">因为很晚了，我们决定回家。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">方式比较</div>
                <div class="keyword text-lg mb-1">Do as I told you.</div>
                <div class="text-sm text-gray-600 mb-1">/du æz aɪ toʊld ju/</div>
                <div class="text-gray-700">按照我告诉你的去做。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">随着变化</div>
                <div class="keyword text-lg mb-1">As time goes by, things change.</div>
                <div class="text-sm text-gray-600 mb-1">/æz taɪm goʊz baɪ, θɪŋz ʧeɪnʤ/</div>
                <div class="text-gray-700">随着时间的流逝，事情会改变。</div>
            </div>
        </div>
    </section>

    <!-- Though - 虽然 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Though - 表示让步（非正式）</h3>
        <p class="text-gray-700 mb-4">Though与although意思相同，但更常用于非正式场合和口语中。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">口语让步</div>
                <div class="keyword text-lg mb-1">Though it's expensive, I'll buy it.</div>
                <div class="text-sm text-gray-600 mb-1">/ðoʊ ɪts ɪkˈspɛnsɪv, aɪl baɪ ɪt/</div>
                <div class="text-gray-700">虽然很贵，但我还是要买。</div>
            </div>
            
            <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">句末使用</div>
                <div class="keyword text-lg mb-1">It's difficult, though.</div>
                <div class="text-sm text-gray-600 mb-1">/ɪts ˈdɪfəkəlt, ðoʊ/</div>
                <div class="text-gray-700">不过，这很困难。</div>
            </div>
            
            <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">日常对话</div>
                <div class="keyword text-lg mb-1">Though he's small, he's strong.</div>
                <div class="text-sm text-gray-600 mb-1">/ðoʊ hiz smɔl, hiz strɔŋ/</div>
                <div class="text-gray-700">虽然他个子小，但很强壮。</div>
            </div>
            
            <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">让步转折</div>
                <div class="keyword text-lg mb-1">I like the movie, though it's long.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ðə ˈmuvi, ðoʊ ɪts lɔŋ/</div>
                <div class="text-gray-700">我喜欢这部电影，尽管它很长。</div>
            </div>
        </div>
    </section>

    <!-- Wherever 和 Whenever -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Wherever 和 Whenever - 表示地点和时间的任意性</h3>
        <p class="text-gray-700 mb-4">Wherever表示"无论在哪里"，Whenever表示"无论何时"，都表示没有限制的情况。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">任意地点</div>
                <div class="keyword text-lg mb-1">Wherever you go, I'll follow you.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛrˈɛvər ju goʊ, aɪl ˈfɑloʊ ju/</div>
                <div class="text-gray-700">无论你去哪里，我都会跟着你。</div>
            </div>
            
            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">任意时间</div>
                <div class="keyword text-lg mb-1">Whenever you need help, call me.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛnˈɛvər ju nid hɛlp, kɔl mi/</div>
                <div class="text-gray-700">无论何时你需要帮助，都给我打电话。</div>
            </div>
            
            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">习惯地点</div>
                <div class="keyword text-lg mb-1">Wherever she sits, she feels comfortable.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛrˈɛvər ʃi sɪts, ʃi filz ˈkʌmftəbəl/</div>
                <div class="text-gray-700">无论她坐在哪里，都感到舒适。</div>
            </div>
            
            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">习惯时间</div>
                <div class="keyword text-lg mb-1">Whenever it rains, I stay inside.</div>
                <div class="text-sm text-gray-600 mb-1">/wɛnˈɛvər ɪt reɪnz, aɪ steɪ ɪnˈsaɪd/</div>
                <div class="text-gray-700">每当下雨时，我就待在室内。</div>
            </div>
        </div>
    </section>

    <!-- So that - 以便/为了 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">So that - 表示目的</h3>
        <p class="text-gray-700 mb-4">So that用于表示目的，说明做某事的原因或意图，相当于"以便、为了"。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">目的说明</div>
                <div class="keyword text-lg mb-1">I study hard so that I can pass the exam.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ ˈstʌdi hɑrd soʊ ðæt aɪ kæn pæs ði ɪɡˈzæm/</div>
                <div class="text-gray-700">我努力学习，以便能通过考试。</div>
            </div>
            
            <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">预防措施</div>
                <div class="keyword text-lg mb-1">Speak slowly so that everyone can understand.</div>
                <div class="text-sm text-gray-600 mb-1">/spik ˈsloʊli soʊ ðæt ˈɛvriˌwʌn kæn ˌʌndərˈstænd/</div>
                <div class="text-gray-700">说慢一点，以便每个人都能理解。</div>
            </div>
            
            <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">帮助他人</div>
                <div class="keyword text-lg mb-1">I'll explain it again so that you understand.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪl ɪkˈspleɪn ɪt əˈɡɛn soʊ ðæt ju ˌʌndərˈstænd/</div>
                <div class="text-gray-700">我会再解释一遍，以便你能理解。</div>
            </div>
            
            <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">未来准备</div>
                <div class="keyword text-lg mb-1">Save money so that you can buy a house.</div>
                <div class="text-sm text-gray-600 mb-1">/seɪv ˈmʌni soʊ ðæt ju kæn baɪ ə haʊs/</div>
                <div class="text-gray-700">存钱，以便你能买房子。</div>
            </div>
        </div>
    </section>

    <!-- Even though 和 Even if -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Even though 和 Even if - 加强语气的连词</h3>
        <p class="text-gray-700 mb-4">Even though表示"即使、尽管"（事实），Even if表示"即使、就算"（假设）。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">事实让步</div>
                <div class="keyword text-lg mb-1">Even though it's raining, we'll go out.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈivən ðoʊ ɪts ˈreɪnɪŋ, wil goʊ aʊt/</div>
                <div class="text-gray-700">即使在下雨，我们也要出去。</div>
            </div>
            
            <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">假设条件</div>
                <div class="keyword text-lg mb-1">Even if you pay me, I won't do it.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈivən ɪf ju peɪ mi, aɪ woʊnt du ɪt/</div>
                <div class="text-gray-700">即使你付钱给我，我也不会做。</div>
            </div>
            
            <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">坚持立场</div>
                <div class="keyword text-lg mb-1">Even though he's my friend, I disagree.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈivən ðoʊ hiz maɪ frɛnd, aɪ ˌdɪsəˈɡri/</div>
                <div class="text-gray-700">即使他是我朋友，我也不同意。</div>
            </div>
            
            <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">极端假设</div>
                <div class="keyword text-lg mb-1">Even if I were rich, I'd still work.</div>
                <div class="text-sm text-gray-600 mb-1">/ˈivən ɪf aɪ wər rɪʧ, aɪd stɪl wɜrk/</div>
                <div class="text-gray-700">即使我很富有，我仍然会工作。</div>
            </div>
        </div>
    </section>

    <!-- In case - 以防 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">In case - 表示预防</h3>
        <p class="text-gray-700 mb-4">In case表示"以防、万一"，用来表示为了预防某种情况而采取的措施。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">预防措施</div>
                <div class="keyword text-lg mb-1">Take an umbrella in case it rains.</div>
                <div class="text-sm text-gray-600 mb-1">/teɪk ən ʌmˈbrɛlə ɪn keɪs ɪt reɪnz/</div>
                <div class="text-gray-700">带把伞，以防下雨。</div>
            </div>
            
            <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">备用计划</div>
                <div class="keyword text-lg mb-1">I'll bring extra food in case we get hungry.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪl brɪŋ ˈɛkstrə fud ɪn keɪs wi ɡɛt ˈhʌŋɡri/</div>
                <div class="text-gray-700">我会带额外的食物，以防我们饿了。</div>
            </div>
            
            <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">安全考虑</div>
                <div class="keyword text-lg mb-1">Call me in case you need help.</div>
                <div class="text-sm text-gray-600 mb-1">/kɔl mi ɪn keɪs ju nid hɛlp/</div>
                <div class="text-gray-700">如果你需要帮助就给我打电话。</div>
            </div>
            
            <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">紧急情况</div>
                <div class="keyword text-lg mb-1">Keep some cash in case of emergency.</div>
                <div class="text-sm text-gray-600 mb-1">/kip sʌm kæʃ ɪn keɪs ʌv ɪˈmɜrʤənsi/</div>
                <div class="text-gray-700">留一些现金以防紧急情况。</div>
            </div>
        </div>
    </section>

    <!-- As long as - 只要 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">As long as - 表示条件</h3>
        <p class="text-gray-700 mb-4">As long as表示"只要"，用来表示满足某个条件就会有某个结果。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">条件要求</div>
                <div class="keyword text-lg mb-1">As long as you work hard, you'll succeed.</div>
                <div class="text-sm text-gray-600 mb-1">/æz lɔŋ æz ju wɜrk hɑrd, jul səkˈsid/</div>
                <div class="text-gray-700">只要你努力工作，你就会成功。</div>
            </div>
            
            <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">持续条件</div>
                <div class="keyword text-lg mb-1">As long as you're happy, I'm happy.</div>
                <div class="text-sm text-gray-600 mb-1">/æz lɔŋ æz jʊr ˈhæpi, aɪm ˈhæpi/</div>
                <div class="text-gray-700">只要你快乐，我就快乐。</div>
            </div>
            
            <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">基本要求</div>
                <div class="keyword text-lg mb-1">As long as it's not expensive, I'll buy it.</div>
                <div class="text-sm text-gray-600 mb-1">/æz lɔŋ æz ɪts nɑt ɪkˈspɛnsɪv, aɪl baɪ ɪt/</div>
                <div class="text-gray-700">只要不贵，我就买。</div>
            </div>
            
            <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">允许条件</div>
                <div class="keyword text-lg mb-1">You can stay as long as you help.</div>
                <div class="text-sm text-gray-600 mb-1">/ju kæn steɪ æz lɔŋ æz ju hɛlp/</div>
                <div class="text-gray-700">只要你帮忙，你就可以留下。</div>
            </div>
        </div>
    </section>

    <!-- Now that - 既然 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">Now that - 表示现在的原因</h3>
        <p class="text-gray-700 mb-4">Now that表示"既然现在"，用来表示基于当前情况的原因或理由。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">现状原因</div>
                <div class="keyword text-lg mb-1">Now that I'm here, let's start the meeting.</div>
                <div class="text-sm text-gray-600 mb-1">/naʊ ðæt aɪm hir, lɛts stɑrt ðə ˈmitɪŋ/</div>
                <div class="text-gray-700">既然我现在在这里，我们开始开会吧。</div>
            </div>
            
            <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">情况变化</div>
                <div class="keyword text-lg mb-1">Now that you're older, you can drive.</div>
                <div class="text-sm text-gray-600 mb-1">/naʊ ðæt jʊr ˈoʊldər, ju kæn draɪv/</div>
                <div class="text-gray-700">既然你现在长大了，你可以开车了。</div>
            </div>
            
            <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">完成状态</div>
                <div class="keyword text-lg mb-1">Now that the work is done, we can relax.</div>
                <div class="text-sm text-gray-600 mb-1">/naʊ ðæt ðə wɜrk ɪz dʌn, wi kæn rɪˈlæks/</div>
                <div class="text-gray-700">既然工作完成了，我们可以放松了。</div>
            </div>
            
            <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">新机会</div>
                <div class="keyword text-lg mb-1">Now that it's summer, let's go swimming.</div>
                <div class="text-sm text-gray-600 mb-1">/naʊ ðæt ɪts ˈsʌmər, lɛts goʊ ˈswɪmɪŋ/</div>
                <div class="text-gray-700">既然现在是夏天，我们去游泳吧。</div>
            </div>
        </div>
    </section>

    <!-- 复杂从属连词组合 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">复杂从属连词组合</h3>
        <p class="text-gray-700 mb-4">在实际使用中，从属连词经常与其他词组合形成更复杂的表达方式。</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">As soon as</div>
                <div class="keyword text-lg mb-1">As soon as I get home, I'll call you.</div>
                <div class="text-sm text-gray-600 mb-1">/æz sun æz aɪ ɡɛt hoʊm, aɪl kɔl ju/</div>
                <div class="text-gray-700">我一到家就给你打电话。</div>
            </div>
            
            <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">As far as</div>
                <div class="keyword text-lg mb-1">As far as I know, he's not coming.</div>
                <div class="text-sm text-gray-600 mb-1">/æz fɑr æz aɪ noʊ, hiz nɑt ˈkʌmɪŋ/</div>
                <div class="text-gray-700">据我所知，他不会来。</div>
            </div>
            
            <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">As if/As though</div>
                <div class="keyword text-lg mb-1">He acts as if he knows everything.</div>
                <div class="text-sm text-gray-600 mb-1">/hi ækts æz ɪf hi noʊz ˈɛvriθɪŋ/</div>
                <div class="text-gray-700">他表现得好像什么都知道。</div>
            </div>
            
            <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">No matter how</div>
                <div class="keyword text-lg mb-1">No matter how hard it is, keep trying.</div>
                <div class="text-sm text-gray-600 mb-1">/noʊ ˈmætər haʊ hɑrd ɪt ɪz, kip ˈtraɪɪŋ/</div>
                <div class="text-gray-700">无论多困难，都要继续努力。</div>
            </div>
        </div>
    </section>

    <!-- 从属连词在不同语境中的使用 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">从属连词的语境应用</h3>
        <p class="text-gray-700 mb-4">从属连词在不同的语境和场合中使用频率和方式会有所不同。以下是常见语境中的应用示例。</p>
        
        <!-- 商务场景 -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">商务场景</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">会议安排</div>
                    <div class="keyword text-lg mb-1">Since everyone is here, let's begin.</div>
                    <div class="text-sm text-gray-600 mb-1">/sɪns ˈɛvriˌwʌn ɪz hir, lɛts bɪˈɡɪn/</div>
                    <div class="text-gray-700">既然大家都到了，我们开始吧。</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">项目进度</div>
                    <div class="keyword text-lg mb-1">Unless we work overtime, we won't finish.</div>
                    <div class="text-sm text-gray-600 mb-1">/ənˈlɛs wi wɜrk ˈoʊvərˌtaɪm, wi woʊnt ˈfɪnɪʃ/</div>
                    <div class="text-gray-700">除非我们加班，否则完不成。</div>
                </div>
            </div>
        </div>
        
        <!-- 日常对话 -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">日常对话</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">朋友聊天</div>
                    <div class="keyword text-lg mb-1">Though I'm tired, I had fun tonight.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðoʊ aɪm ˈtaɪrd, aɪ hæd fʌn təˈnaɪt/</div>
                    <div class="text-gray-700">虽然我很累，但今晚很开心。</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">家庭对话</div>
                    <div class="keyword text-lg mb-1">When dinner is ready, I'll call you.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn ˈdɪnər ɪz ˈrɛdi, aɪl kɔl ju/</div>
                    <div class="text-gray-700">晚饭准备好后，我会叫你。</div>
                </div>
            </div>
        </div>
        
        <!-- 学术写作 -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">学术写作</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">论点阐述</div>
                    <div class="keyword text-lg mb-1">Although the results vary, the trend is clear.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ðə rɪˈzʌlts ˈvɛri, ðə trɛnd ɪz klɪr/</div>
                    <div class="text-gray-700">尽管结果有所不同，但趋势很明显。</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">因果关系</div>
                    <div class="keyword text-lg mb-1">Because the data is limited, more research is needed.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈkɔz ðə ˈdeɪtə ɪz ˈlɪmətəd, mɔr rɪˈsɜrʧ ɪz ˈnidəd/</div>
                    <div class="text-gray-700">因为数据有限，需要更多研究。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 常见搭配错误及纠正 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">常见错误及纠正</h3>
        <p class="text-gray-700 mb-4">以下是学习者在使用从属连词时常犯的错误及正确用法。</p>
        
        <div class="grid grid-cols-1 gap-4 mb-6">
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">错误示例</div>
                <div class="text-lg mb-1 text-red-600">Because I'm tired, so I want to sleep.</div>
                <div class="text-sm text-gray-600 mb-1">❌ 不能同时使用because和so</div>
                <div class="text-gray-700"><span class="keyword">正确：</span>Because I'm tired, I want to sleep. 或 I'm tired, so I want to sleep.</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">错误示例</div>
                <div class="text-lg mb-1 text-red-600">Although it's raining, but we'll go out.</div>
                <div class="text-sm text-gray-600 mb-1">❌ 不能同时使用although和but</div>
                <div class="text-gray-700"><span class="keyword">正确：</span>Although it's raining, we'll go out. 或 It's raining, but we'll go out.</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">错误示例</div>
                <div class="text-lg mb-1 text-red-600">When will you come back?</div>
                <div class="text-sm text-gray-600 mb-1">❌ 这是疑问词，不是从属连词</div>
                <div class="text-gray-700"><span class="keyword">正确：</span>When you come back, call me. (从属连词用法)</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">错误示例</div>
                <div class="text-lg mb-1 text-red-600">I will go unless it will rain.</div>
                <div class="text-sm text-gray-600 mb-1">❌ 时间/条件从句中不用will</div>
                <div class="text-gray-700"><span class="keyword">正确：</span>I will go unless it rains.</div>
            </div>
        </div>
    </section>

    <!-- 总结 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">从属连词使用要点总结</h3>
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <ul class="space-y-3 text-gray-700">
                <li><span class="keyword">位置规则：</span>从句可以放在主句前面或后面。从句在前时用逗号分隔，从句在后时通常不用逗号。</li>
                <li><span class="keyword">时态协调：</span>主句和从句的时态要保持逻辑上的一致性。在时间和条件从句中，用现在时表示将来。</li>
                <li><span class="keyword">意义明确：</span>选择合适的从属连词来准确表达句子间的逻辑关系。</li>
                <li><span class="keyword">避免重复：</span>不要在同一个句子中使用多个表示相同关系的连词（如because...so, although...but）。</li>
                <li><span class="keyword">语序正确：</span>从句中要使用正常的主谓语序，不像疑问句那样倒装。</li>
                <li><span class="keyword">语境适应：</span>根据正式程度选择合适的连词（如though vs although）。</li>
                <li><span class="keyword">语气强弱：</span>某些连词带有语气强弱的区别（如even though比though语气更强）。</li>
            </ul>
        </div>
    </section>

    <!-- 实用记忆法 -->
    <section class="mb-8">
        <h3 class="text-xl font-bold mb-4 text-gray-800">从属连词记忆方法</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="font-semibold mb-2 text-blue-800">按功能分类记忆</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li><span class="keyword">时间：</span>when, while, before, after, since, until, as soon as</li>
                    <li><span class="keyword">原因：</span>because, since, as, now that</li>
                    <li><span class="keyword">条件：</span>if, unless, as long as, in case</li>
                    <li><span class="keyword">让步：</span>although, though, even though, even if</li>
                    <li><span class="keyword">目的：</span>so that, in order that</li>
                </ul>
            </div>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 class="font-semibold mb-2 text-green-800">造句练习法</h4>
                <ul class="text-sm text-green-700 space-y-1">
                    <li>每天选择3-5个连词造句</li>
                    <li>结合日常生活场景练习</li>
                    <li>注意从句和主句的逻辑关系</li>
                    <li>多读英文文章，观察连词使用</li>
                    <li>与他人对话时有意识地使用</li>
                </ul>
            </div>
        </div>
    </section>

</body>
</html>